<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Setting extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'key',
        'value',
        'type',
        'group',
        'description',
    ];

    /**
     * The attributes that should be cast.
     *
     * @return array<string, string>
     */
    protected function casts(): array
    {
        return [
            'value' => 'string',
        ];
    }

    /**
     * Get a setting value by key.
     */
    public static function get($key, $default = null)
    {
        $setting = static::where('key', $key)->first();
        
        if (!$setting) {
            return $default;
        }

        return static::castValue($setting->value, $setting->type);
    }

    /**
     * Set a setting value by key.
     */
    public static function set($key, $value, $type = 'string', $group = 'general', $description = null)
    {
        return static::updateOrCreate(
            ['key' => $key],
            [
                'value' => $value,
                'type' => $type,
                'group' => $group,
                'description' => $description,
            ]
        );
    }

    /**
     * Get all settings as key-value pairs.
     */
    public static function getAll()
    {
        return static::all()->mapWithKeys(function ($setting) {
            return [$setting->key => static::castValue($setting->value, $setting->type)];
        });
    }

    /**
     * Get settings by group.
     */
    public static function getByGroup($group)
    {
        return static::where('group', $group)->get()->mapWithKeys(function ($setting) {
            return [$setting->key => static::castValue($setting->value, $setting->type)];
        });
    }

    /**
     * Cast value to appropriate type.
     */
    protected static function castValue($value, $type)
    {
        switch ($type) {
            case 'boolean':
                return filter_var($value, FILTER_VALIDATE_BOOLEAN);
            case 'integer':
                return (int) $value;
            case 'float':
                return (float) $value;
            case 'array':
                return json_decode($value, true) ?: [];
            case 'json':
                return json_decode($value, true);
            default:
                return $value;
        }
    }

    /**
     * Get default system settings.
     */
    public static function getDefaults()
    {
        return [
            // Company Settings
            'company_name' => ['value' => 'نظام المالية', 'type' => 'string', 'group' => 'company', 'description' => 'اسم الشركة'],
            'company_address' => ['value' => '', 'type' => 'string', 'group' => 'company', 'description' => 'عنوان الشركة'],
            'company_phone' => ['value' => '', 'type' => 'string', 'group' => 'company', 'description' => 'هاتف الشركة'],
            'company_email' => ['value' => '', 'type' => 'string', 'group' => 'company', 'description' => 'بريد الشركة الإلكتروني'],
            'company_tax_number' => ['value' => '', 'type' => 'string', 'group' => 'company', 'description' => 'الرقم الضريبي'],
            'company_logo' => ['value' => '', 'type' => 'string', 'group' => 'company', 'description' => 'شعار الشركة'],

            // Currency Settings
            'currency_code' => ['value' => 'EGP', 'type' => 'string', 'group' => 'currency', 'description' => 'رمز العملة'],
            'currency_symbol' => ['value' => 'ج.م', 'type' => 'string', 'group' => 'currency', 'description' => 'رمز العملة'],
            'currency_position' => ['value' => 'after', 'type' => 'string', 'group' => 'currency', 'description' => 'موضع رمز العملة'],

            // Tax Settings
            'tax_enabled' => ['value' => true, 'type' => 'boolean', 'group' => 'tax', 'description' => 'تفعيل الضريبة'],
            'tax_rate' => ['value' => 14, 'type' => 'float', 'group' => 'tax', 'description' => 'معدل الضريبة %'],
            'tax_name' => ['value' => 'ضريبة القيمة المضافة', 'type' => 'string', 'group' => 'tax', 'description' => 'اسم الضريبة'],

            // Invoice Settings
            'invoice_prefix' => ['value' => 'INV', 'type' => 'string', 'group' => 'invoice', 'description' => 'بادئة رقم الفاتورة'],
            'invoice_footer' => ['value' => 'شكراً لتعاملكم معنا', 'type' => 'string', 'group' => 'invoice', 'description' => 'تذييل الفاتورة'],
            'print_after_sale' => ['value' => true, 'type' => 'boolean', 'group' => 'invoice', 'description' => 'طباعة تلقائية بعد البيع'],

            // Inventory Settings
            'low_stock_threshold' => ['value' => 10, 'type' => 'integer', 'group' => 'inventory', 'description' => 'حد المخزون المنخفض'],
            'track_inventory' => ['value' => true, 'type' => 'boolean', 'group' => 'inventory', 'description' => 'تتبع المخزون'],
            'allow_negative_stock' => ['value' => false, 'type' => 'boolean', 'group' => 'inventory', 'description' => 'السماح بالمخزون السالب'],

            // System Settings
            'timezone' => ['value' => 'Africa/Cairo', 'type' => 'string', 'group' => 'system', 'description' => 'المنطقة الزمنية'],
            'date_format' => ['value' => 'd/m/Y', 'type' => 'string', 'group' => 'system', 'description' => 'تنسيق التاريخ'],
            'time_format' => ['value' => 'H:i', 'type' => 'string', 'group' => 'system', 'description' => 'تنسيق الوقت'],
            'backup_enabled' => ['value' => true, 'type' => 'boolean', 'group' => 'system', 'description' => 'تفعيل النسخ الاحتياطي'],
        ];
    }

    /**
     * Initialize default settings.
     */
    public static function initializeDefaults()
    {
        $defaults = static::getDefaults();

        foreach ($defaults as $key => $config) {
            static::firstOrCreate(
                ['key' => $key],
                [
                    'value' => $config['value'],
                    'type' => $config['type'],
                    'group' => $config['group'],
                    'description' => $config['description'],
                ]
            );
        }
    }

    /**
     * Get setting groups.
     */
    public static function getGroups()
    {
        return [
            'company' => 'إعدادات الشركة',
            'currency' => 'إعدادات العملة',
            'tax' => 'إعدادات الضريبة',
            'invoice' => 'إعدادات الفواتير',
            'inventory' => 'إعدادات المخزون',
            'system' => 'إعدادات النظام',
        ];
    }
}
