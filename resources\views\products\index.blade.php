@extends('layouts.app')

@section('content')
<div x-data="products()" class="container mx-auto py-6">
    <!-- Header Section -->
    <div class="neumorphic p-6 rounded-2xl mb-6">
        <div class="flex flex-col lg:flex-row justify-between items-start lg:items-center gap-4">
            <div>
                <h2 class="text-3xl font-bold text-gray-800 mb-2">إدارة المنتجات</h2>
                <p class="text-gray-600">إدارة وتنظيم جميع المنتجات في النظام</p>
            </div>
            <div class="flex flex-col sm:flex-row gap-3 w-full lg:w-auto">
                <button class="btn-success flex items-center gap-2 justify-center" @click="addProduct()">
                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                    </svg>
                    إضافة منتج جديد
                </button>
                <button class="btn-primary flex items-center gap-2 justify-center" @click="generateSampleData()">
                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-8l-4-4m0 0L8 8m4-4v12"></path>
                    </svg>
                    بيانات تجريبية
                </button>
            </div>
        </div>
    </div>

    <!-- Statistics Cards -->
    <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
        <div class="neumorphic p-6 rounded-2xl">
            <div class="flex items-center justify-between">
                <div>
                    <p class="text-gray-600 text-sm">إجمالي المنتجات</p>
                    <p class="text-2xl font-bold text-primary" x-text="products.length"></p>
                </div>
                <div class="w-12 h-12 bg-blue-100 rounded-xl flex items-center justify-center">
                    <svg class="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4"></path>
                    </svg>
                </div>
            </div>
        </div>

        <div class="neumorphic p-6 rounded-2xl">
            <div class="flex items-center justify-between">
                <div>
                    <p class="text-gray-600 text-sm">المنتجات المنخفضة</p>
                    <p class="text-2xl font-bold text-orange-500" x-text="lowStockProducts"></p>
                </div>
                <div class="w-12 h-12 bg-orange-100 rounded-xl flex items-center justify-center">
                    <svg class="w-6 h-6 text-orange-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
                    </svg>
                </div>
            </div>
        </div>

        <div class="neumorphic p-6 rounded-2xl">
            <div class="flex items-center justify-between">
                <div>
                    <p class="text-gray-600 text-sm">إجمالي القيمة</p>
                    <p class="text-2xl font-bold text-green-500" x-text="formatCurrency(totalValue)"></p>
                </div>
                <div class="w-12 h-12 bg-green-100 rounded-xl flex items-center justify-center">
                    <svg class="w-6 h-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"></path>
                    </svg>
                </div>
            </div>
        </div>
    </div>

    <!-- Search and Filter Section -->
    <div class="neumorphic p-6 rounded-2xl mb-6">
        <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div class="relative">
                <input type="text"
                       placeholder="بحث باسم المنتج أو الباركود..."
                       class="neumorphic-inner w-full p-4 pr-12 rounded-xl"
                       x-model="searchQuery">
                <svg class="absolute right-4 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                </svg>
            </div>

            <select class="neumorphic-inner w-full p-4 rounded-xl" x-model="selectedCategory">
                <option value="">جميع التصنيفات</option>
                <template x-for="category in categories" :key="category">
                    <option :value="category" x-text="category"></option>
                </template>
            </select>

            <select class="neumorphic-inner w-full p-4 rounded-xl" x-model="sortBy">
                <option value="name">ترتيب حسب الاسم</option>
                <option value="price">ترتيب حسب السعر</option>
                <option value="quantity">ترتيب حسب الكمية</option>
                <option value="category">ترتيب حسب التصنيف</option>
            </select>
        </div>
    </div>

    <!-- Products Table -->
    <div class="neumorphic p-6 rounded-2xl">
        <div class="overflow-x-auto">
            <table class="table-modern w-full">
                <thead>
                    <tr>
                        <th class="text-right">#</th>
                        <th class="text-right">الصورة</th>
                        <th class="text-right">اسم المنتج</th>
                        <th class="text-right">التصنيف</th>
                        <th class="text-right">السعر</th>
                        <th class="text-right">الكمية</th>
                        <th class="text-right">الباركود</th>
                        <th class="text-right">الحالة</th>
                        <th class="text-right">الإجراءات</th>
                    </tr>
                </thead>
                <tbody>
                    <template x-for="(product, index) in filteredProducts" :key="product.id">
                        <tr class="slide-up">
                            <td x-text="index + 1"></td>
                            <td>
                                <div class="w-16 h-16 bg-gradient-to-br from-gray-100 to-gray-200 border-2 border-dashed border-gray-300 rounded-xl flex items-center justify-center">
                                    <svg class="w-6 h-6 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                                    </svg>
                                </div>
                            </td>
                            <td>
                                <div>
                                    <p class="font-semibold text-gray-800" x-text="product.name"></p>
                                    <p class="text-sm text-gray-500" x-text="'كود: ' + product.id"></p>
                                </div>
                            </td>
                            <td>
                                <span class="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800" x-text="product.category"></span>
                            </td>
                            <td>
                                <span class="font-semibold text-green-600" x-text="formatCurrency(product.price)"></span>
                            </td>
                            <td>
                                <div class="flex items-center gap-2">
                                    <span x-text="product.quantity"></span>
                                    <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium"
                                          :class="product.quantity <= 10 ? 'bg-red-100 text-red-800' : product.quantity <= 50 ? 'bg-yellow-100 text-yellow-800' : 'bg-green-100 text-green-800'">
                                        <span x-text="product.quantity <= 10 ? 'منخفض' : product.quantity <= 50 ? 'متوسط' : 'جيد'"></span>
                                    </span>
                                </div>
                            </td>
                            <td>
                                <code class="bg-gray-100 px-2 py-1 rounded text-sm" x-text="product.barcode || 'غير محدد'"></code>
                            </td>
                            <td>
                                <span class="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium"
                                      :class="product.quantity > 0 ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'">
                                    <span x-text="product.quantity > 0 ? 'متوفر' : 'نفد المخزون'"></span>
                                </span>
                            </td>
                            <td>
                                <div class="flex items-center gap-2">
                                    <button class="p-2 text-blue-600 hover:bg-blue-50 rounded-lg transition-colors"
                                            @click="editProduct(product)"
                                            title="تعديل">
                                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                                        </svg>
                                    </button>
                                    <button class="p-2 text-red-600 hover:bg-red-50 rounded-lg transition-colors"
                                            @click="confirmDelete(product)"
                                            title="حذف">
                                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                                        </svg>
                                    </button>
                                </div>
                            </td>
                        </tr>
                    </template>

                    <!-- Empty State -->
                    <tr x-show="filteredProducts.length === 0">
                        <td colspan="9" class="text-center py-12">
                            <div class="flex flex-col items-center gap-4">
                                <svg class="w-16 h-16 text-gray-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 13V6a2 2 0 00-2-2H6a2 2 0 00-2 2v7m16 0v5a2 2 0 01-2 2H6a2 2 0 01-2-2v-5m16 0h-2.586a1 1 0 00-.707.293l-2.414 2.414a1 1 0 01-.707.293h-3.172a1 1 0 01-.707-.293l-2.414-2.414A1 1 0 006.586 13H4"></path>
                                </svg>
                                <div class="text-center">
                                    <h3 class="text-lg font-semibold text-gray-600 mb-2">لا توجد منتجات</h3>
                                    <p class="text-gray-500 mb-4">ابدأ بإضافة منتجات جديدة لإدارة مخزونك</p>
                                    <button class="btn-primary" @click="addProduct()">إضافة منتج جديد</button>
                                </div>
                            </div>
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>
    </div>
</div>

<!-- Add/Edit Product Modal -->
<div x-show="showModal"
     x-transition:enter="transition ease-out duration-300"
     x-transition:enter-start="opacity-0"
     x-transition:enter-end="opacity-100"
     x-transition:leave="transition ease-in duration-200"
     x-transition:leave-start="opacity-100"
     x-transition:leave-end="opacity-0"
     class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50"
     @click.self="showModal = false">
    <div class="neumorphic w-full max-w-2xl p-8 rounded-2xl slide-up"
         x-transition:enter="transition ease-out duration-300"
         x-transition:enter-start="opacity-0 transform scale-95"
         x-transition:enter-end="opacity-100 transform scale-100"
         x-transition:leave="transition ease-in duration-200"
         x-transition:leave-start="opacity-100 transform scale-100"
         x-transition:leave-end="opacity-0 transform scale-95">

        <div class="flex items-center gap-3 mb-6">
            <div class="w-10 h-10 bg-gradient-to-br from-blue-500 to-blue-600 rounded-xl flex items-center justify-center">
                <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4"></path>
                </svg>
            </div>
            <h2 class="text-2xl font-bold text-gray-800" x-text="modalTitle"></h2>
        </div>

        <form @submit.prevent="saveProduct" class="space-y-6">
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div class="md:col-span-2">
                    <label class="block text-gray-700 font-semibold mb-3">اسم المنتج *</label>
                    <input type="text"
                           class="neumorphic-inner w-full p-4 rounded-xl text-lg"
                           x-model="currentProduct.name"
                           placeholder="أدخل اسم المنتج"
                           required>
                </div>

                <div>
                    <label class="block text-gray-700 font-semibold mb-3">التصنيف *</label>
                    <input type="text"
                           class="neumorphic-inner w-full p-4 rounded-xl"
                           x-model="currentProduct.category"
                           placeholder="مثال: إلكترونيات"
                           list="categories"
                           required>
                    <datalist id="categories">
                        <template x-for="category in categories" :key="category">
                            <option :value="category"></option>
                        </template>
                    </datalist>
                </div>

                <div>
                    <label class="block text-gray-700 font-semibold mb-3">الباركود</label>
                    <div class="relative">
                        <input type="text"
                               class="neumorphic-inner w-full p-4 pr-12 rounded-xl"
                               x-model="currentProduct.barcode"
                               placeholder="اختياري">
                        <button type="button"
                                class="absolute left-3 top-1/2 transform -translate-y-1/2 text-blue-600 hover:text-blue-800"
                                @click="generateBarcode()"
                                title="توليد باركود تلقائي">
                            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                            </svg>
                        </button>
                    </div>
                </div>

                <div>
                    <label class="block text-gray-700 font-semibold mb-3">السعر (ج.م) *</label>
                    <input type="number"
                           step="0.01"
                           min="0"
                           class="neumorphic-inner w-full p-4 rounded-xl text-lg font-semibold"
                           x-model="currentProduct.price"
                           placeholder="0.00"
                           required>
                </div>

                <div>
                    <label class="block text-gray-700 font-semibold mb-3">الكمية *</label>
                    <input type="number"
                           min="0"
                           class="neumorphic-inner w-full p-4 rounded-xl text-lg font-semibold"
                           x-model="currentProduct.quantity"
                           placeholder="0"
                           required>
                </div>

                <div class="md:col-span-2">
                    <label class="block text-gray-700 font-semibold mb-3">وصف المنتج</label>
                    <textarea class="neumorphic-inner w-full p-4 rounded-xl resize-none"
                              rows="3"
                              x-model="currentProduct.description"
                              placeholder="وصف اختياري للمنتج"></textarea>
                </div>
            </div>

            <div class="flex flex-col sm:flex-row justify-end gap-3 pt-6 border-t border-gray-200">
                <button type="button"
                        class="btn-danger order-2 sm:order-1"
                        @click="showModal = false">
                    إلغاء
                </button>
                <button type="submit"
                        class="btn-primary order-1 sm:order-2 flex items-center justify-center gap-2"
                        :disabled="loading">
                    <div x-show="loading" class="spinner"></div>
                    <span x-text="loading ? 'جاري الحفظ...' : 'حفظ المنتج'"></span>
                </button>
            </div>
        </form>
    </div>
</div>

<!-- Delete Confirmation Modal -->
<div x-show="showDeleteModal"
     x-transition:enter="transition ease-out duration-300"
     x-transition:enter-start="opacity-0"
     x-transition:enter-end="opacity-100"
     x-transition:leave="transition ease-in duration-200"
     x-transition:leave-start="opacity-100"
     x-transition:leave-end="opacity-0"
     class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50"
     @click.self="showDeleteModal = false">
    <div class="neumorphic w-full max-w-md p-8 rounded-2xl slide-up"
         x-transition:enter="transition ease-out duration-300"
         x-transition:enter-start="opacity-0 transform scale-95"
         x-transition:enter-end="opacity-100 transform scale-100"
         x-transition:leave="transition ease-in duration-200"
         x-transition:leave-start="opacity-100 transform scale-100"
         x-transition:leave-end="opacity-0 transform scale-95">

        <div class="text-center mb-6">
            <div class="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <svg class="w-8 h-8 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                </svg>
            </div>
            <h2 class="text-2xl font-bold text-gray-800 mb-2">تأكيد الحذف</h2>
            <p class="text-gray-600 mb-2">هل أنت متأكد من حذف المنتج</p>
            <p class="font-semibold text-gray-800" x-text="'\"' + currentProduct.name + '\"'"></p>
            <p class="text-sm text-red-600 mt-2">لا يمكن التراجع عن هذا الإجراء</p>
        </div>

        <div class="flex flex-col sm:flex-row justify-center gap-3">
            <button type="button"
                    class="btn-primary order-2 sm:order-1"
                    @click="showDeleteModal = false">
                إلغاء
            </button>
            <button type="button"
                    class="btn-danger order-1 sm:order-2 flex items-center justify-center gap-2"
                    @click="deleteProduct"
                    :disabled="loading">
                <div x-show="loading" class="spinner"></div>
                <span x-text="loading ? 'جاري الحذف...' : 'تأكيد الحذف'"></span>
            </button>
        </div>
    </div>
</div>

<script>
    document.addEventListener('alpine:init', () => {
        Alpine.data('products', () => ({
            products: [],
            searchQuery: '',
            selectedCategory: '',
            sortBy: 'name',
            showModal: false,
            showDeleteModal: false,
            isEditing: false,
            loading: false,
            currentProduct: {
                id: null,
                name: '',
                category: '',
                price: 0,
                quantity: 0,
                barcode: '',
                description: ''
            },

            init() {
                this.loadProducts();
                // Show welcome message if no products
                if (this.products.length === 0) {
                    setTimeout(() => {
                        this.showNotification('مرحباً! ابدأ بإضافة منتجات جديدة لإدارة مخزونك', 'info');
                    }, 1000);
                }
            },

            loadProducts() {
                const storedProducts = localStorage.getItem('products');
                this.products = storedProducts ? JSON.parse(storedProducts) : [];
            },

            get categories() {
                const cats = [...new Set(this.products.map(p => p.category).filter(Boolean))];
                return cats.sort();
            },

            get filteredProducts() {
                let filtered = this.products;

                // Filter by search query
                if (this.searchQuery) {
                    const query = this.searchQuery.toLowerCase();
                    filtered = filtered.filter(product =>
                        product.name.toLowerCase().includes(query) ||
                        (product.barcode && product.barcode.includes(query)) ||
                        (product.category && product.category.toLowerCase().includes(query))
                    );
                }

                // Filter by category
                if (this.selectedCategory) {
                    filtered = filtered.filter(product => product.category === this.selectedCategory);
                }

                // Sort products
                filtered.sort((a, b) => {
                    switch (this.sortBy) {
                        case 'name':
                            return a.name.localeCompare(b.name, 'ar');
                        case 'price':
                            return parseFloat(b.price) - parseFloat(a.price);
                        case 'quantity':
                            return parseInt(b.quantity) - parseInt(a.quantity);
                        case 'category':
                            return (a.category || '').localeCompare(b.category || '', 'ar');
                        default:
                            return 0;
                    }
                });

                return filtered;
            },

            get lowStockProducts() {
                return this.products.filter(p => p.quantity <= 10).length;
            },

            get totalValue() {
                return this.products.reduce((total, product) => {
                    return total + (parseFloat(product.price) * parseInt(product.quantity));
                }, 0);
            },

            get modalTitle() {
                return this.isEditing ? 'تعديل المنتج' : 'إضافة منتج جديد';
            },

            formatCurrency(amount) {
                return new Intl.NumberFormat('ar-EG', {
                    style: 'currency',
                    currency: 'EGP',
                    minimumFractionDigits: 2
                }).format(amount || 0);
            },

            generateBarcode() {
                // Generate a simple barcode
                const timestamp = Date.now().toString();
                const random = Math.floor(Math.random() * 1000).toString().padStart(3, '0');
                this.currentProduct.barcode = timestamp.slice(-6) + random;
            },

            generateSampleData() {
                if (confirm('هل تريد إضافة بيانات تجريبية؟ سيتم إضافة عدة منتجات كأمثلة.')) {
                    const sampleProducts = [
                        {
                            id: Date.now().toString() + '1',
                            name: 'لابتوب Dell Inspiron',
                            category: 'إلكترونيات',
                            price: 15000,
                            quantity: 25,
                            barcode: '123456789001',
                            description: 'لابتوب عالي الأداء للاستخدام المكتبي'
                        },
                        {
                            id: Date.now().toString() + '2',
                            name: 'هاتف Samsung Galaxy',
                            category: 'إلكترونيات',
                            price: 8500,
                            quantity: 15,
                            barcode: '123456789002',
                            description: 'هاتف ذكي بمواصفات متقدمة'
                        },
                        {
                            id: Date.now().toString() + '3',
                            name: 'قميص قطني',
                            category: 'ملابس',
                            price: 250,
                            quantity: 100,
                            barcode: '123456789003',
                            description: 'قميص قطني عالي الجودة'
                        },
                        {
                            id: Date.now().toString() + '4',
                            name: 'كتاب البرمجة',
                            category: 'كتب',
                            price: 120,
                            quantity: 50,
                            barcode: '123456789004',
                            description: 'كتاب تعليمي في البرمجة'
                        },
                        {
                            id: Date.now().toString() + '5',
                            name: 'ساعة يد رياضية',
                            category: 'إكسسوارات',
                            price: 450,
                            quantity: 8,
                            barcode: '123456789005',
                            description: 'ساعة رياضية مقاومة للماء'
                        }
                    ];

                    this.products = [...this.products, ...sampleProducts];
                    localStorage.setItem('products', JSON.stringify(this.products));
                    this.showNotification('تم إضافة البيانات التجريبية بنجاح!', 'success');
                }
            },

            addProduct() {
                this.isEditing = false;
                this.currentProduct = {
                    id: null,
                    name: '',
                    category: '',
                    price: 0,
                    quantity: 0,
                    barcode: '',
                    description: ''
                };
                this.showModal = true;
            },

            editProduct(product) {
                this.isEditing = true;
                this.currentProduct = {...product};
                this.showModal = true;
            },

            confirmDelete(product) {
                this.currentProduct = {...product};
                this.showDeleteModal = true;
            },

            async saveProduct() {
                // Validation
                if (!this.currentProduct.name.trim()) {
                    this.showNotification('يرجى إدخال اسم المنتج', 'error');
                    return;
                }

                if (!this.currentProduct.category.trim()) {
                    this.showNotification('يرجى إدخال تصنيف المنتج', 'error');
                    return;
                }

                if (this.currentProduct.price <= 0) {
                    this.showNotification('يرجى إدخال سعر صحيح', 'error');
                    return;
                }

                if (this.currentProduct.quantity < 0) {
                    this.showNotification('يرجى إدخال كمية صحيحة', 'error');
                    return;
                }

                this.loading = true;

                // Simulate API call
                await new Promise(resolve => setTimeout(resolve, 1000));

                try {
                    if (this.isEditing) {
                        const index = this.products.findIndex(p => p.id === this.currentProduct.id);
                        if (index !== -1) {
                            this.products[index] = {...this.currentProduct};
                            this.showNotification('تم تحديث المنتج بنجاح!', 'success');
                        }
                    } else {
                        this.currentProduct.id = Date.now().toString();
                        this.products.push({...this.currentProduct});
                        this.showNotification('تم إضافة المنتج بنجاح!', 'success');
                    }

                    localStorage.setItem('products', JSON.stringify(this.products));
                    this.showModal = false;
                } catch (error) {
                    this.showNotification('حدث خطأ أثناء حفظ المنتج', 'error');
                } finally {
                    this.loading = false;
                }
            },

            async deleteProduct() {
                this.loading = true;

                // Simulate API call
                await new Promise(resolve => setTimeout(resolve, 800));

                try {
                    this.products = this.products.filter(p => p.id !== this.currentProduct.id);
                    localStorage.setItem('products', JSON.stringify(this.products));
                    this.showDeleteModal = false;
                    this.showNotification('تم حذف المنتج بنجاح!', 'success');
                } catch (error) {
                    this.showNotification('حدث خطأ أثناء حذف المنتج', 'error');
                } finally {
                    this.loading = false;
                }
            },

            showNotification(message, type = 'success') {
                // Use the global notification function from the layout
                if (window.appData && typeof window.appData().showNotification === 'function') {
                    window.appData().showNotification(message, type);
                } else {
                    // Fallback to alert
                    alert(message);
                }
            }
        }));
    });
</script>
@endsection