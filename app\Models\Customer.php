<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class Customer extends Model
{
    use HasFactory, SoftDeletes;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'name',
        'email',
        'phone',
        'address',
        'city',
        'postal_code',
        'tax_number',
        'credit_limit',
        'is_active',
    ];

    /**
     * The attributes that should be cast.
     *
     * @return array<string, string>
     */
    protected function casts(): array
    {
        return [
            'credit_limit' => 'decimal:2',
            'is_active' => 'boolean',
        ];
    }

    /**
     * Get the sales for the customer.
     */
    public function sales()
    {
        return $this->hasMany(Sale::class);
    }

    /**
     * Scope a query to only include active customers.
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Get the total purchases for the customer.
     */
    public function getTotalPurchasesAttribute()
    {
        return $this->sales()->sum('total_amount');
    }

    /**
     * Get the total outstanding balance for the customer.
     */
    public function getOutstandingBalanceAttribute()
    {
        return $this->sales()
            ->where('payment_status', '!=', 'paid')
            ->sum('total_amount') - $this->sales()
            ->where('payment_status', '!=', 'paid')
            ->sum('paid_amount');
    }

    /**
     * Get the number of orders for the customer.
     */
    public function getOrdersCountAttribute()
    {
        return $this->sales()->count();
    }

    /**
     * Check if customer has outstanding balance.
     */
    public function hasOutstandingBalance()
    {
        return $this->outstanding_balance > 0;
    }

    /**
     * Check if customer is within credit limit.
     */
    public function isWithinCreditLimit()
    {
        if ($this->credit_limit <= 0) {
            return true;
        }
        return $this->outstanding_balance <= $this->credit_limit;
    }

    /**
     * Get the last purchase date.
     */
    public function getLastPurchaseDateAttribute()
    {
        $lastSale = $this->sales()->latest()->first();
        return $lastSale ? $lastSale->created_at : null;
    }

    /**
     * Get customer's full address.
     */
    public function getFullAddressAttribute()
    {
        $address = $this->address;
        if ($this->city) {
            $address .= ', ' . $this->city;
        }
        if ($this->postal_code) {
            $address .= ' ' . $this->postal_code;
        }
        return $address;
    }
}
