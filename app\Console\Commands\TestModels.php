<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\User;
use App\Models\Category;
use App\Models\Product;
use App\Models\Customer;
use App\Models\Sale;
use App\Models\SaleItem;
use App\Models\Expense;
use App\Models\Setting;

class TestModels extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'test:models';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Test all models and relationships';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('🚀 اختبار النماذج (Models) والعلاقات');
        $this->info('=====================================');

        try {
            // 1. اختبار المستخدم
            $this->info('1️⃣ اختبار نموذج User:');
            $user = User::create([
                'name' => 'أحمد محمد',
                'email' => '<EMAIL>',
                'password' => bcrypt('password'),
                'role' => 'admin',
                'phone' => '01234567890',
                'is_active' => true
            ]);
            $this->line("✅ تم إنشاء المستخدم: {$user->name} (ID: {$user->id})");
            $this->line("   الدور: {$user->role}, مدير؟ " . ($user->isAdmin() ? 'نعم' : 'لا'));

            // 2. اختبار التصنيف
            $this->info('2️⃣ اختبار نموذج Category:');
            $category = Category::create([
                'name' => 'إلكترونيات',
                'description' => 'أجهزة إلكترونية ومعدات تقنية',
                'is_active' => true
            ]);
            $this->line("✅ تم إنشاء التصنيف: {$category->name} (ID: {$category->id})");

            // 3. اختبار المنتج
            $this->info('3️⃣ اختبار نموذج Product:');
            $product = Product::create([
                'name' => 'لابتوب Dell',
                'description' => 'لابتوب عالي الأداء',
                'barcode' => '123456789',
                'price' => 15000.00,
                'cost_price' => 12000.00,
                'quantity' => 50,
                'min_quantity' => 10,
                'category_id' => $category->id,
                'is_active' => true
            ]);
            $this->line("✅ تم إنشاء المنتج: {$product->name} (ID: {$product->id})");
            $this->line("   السعر: {$product->price} ج.م, الكمية: {$product->quantity}");
            $this->line("   هامش الربح: " . number_format($product->profit_margin, 2) . "%");
            $this->line("   في المخزون؟ " . ($product->isInStock() ? 'نعم' : 'لا'));

            // 4. اختبار العميل
            $this->info('4️⃣ اختبار نموذج Customer:');
            $customer = Customer::create([
                'name' => 'محمد علي',
                'email' => '<EMAIL>',
                'phone' => '01098765432',
                'address' => 'شارع النيل، المعادي',
                'city' => 'القاهرة',
                'credit_limit' => 5000.00,
                'is_active' => true
            ]);
            $this->line("✅ تم إنشاء العميل: {$customer->name} (ID: {$customer->id})");

            // 5. اختبار المبيعة
            $this->info('5️⃣ اختبار نموذج Sale:');
            $sale = Sale::create([
                'customer_id' => $customer->id,
                'user_id' => $user->id,
                'sale_date' => now(),
                'subtotal' => 15000.00,
                'tax_amount' => 2100.00,
                'total_amount' => 17100.00,
                'paid_amount' => 17100.00,
                'payment_method' => 'cash',
                'payment_status' => 'paid',
                'status' => 'completed'
            ]);
            $this->line("✅ تم إنشاء المبيعة: {$sale->invoice_number} (ID: {$sale->id})");

            // 6. اختبار عنصر المبيعة
            $this->info('6️⃣ اختبار نموذج SaleItem:');
            $saleItem = SaleItem::create([
                'sale_id' => $sale->id,
                'product_id' => $product->id,
                'quantity' => 1,
                'unit_price' => 15000.00,
                'total_price' => 15000.00
            ]);
            $this->line("✅ تم إنشاء عنصر المبيعة: {$saleItem->product_name} (ID: {$saleItem->id})");

            // 7. اختبار المصروف
            $this->info('7️⃣ اختبار نموذج Expense:');
            $expense = Expense::create([
                'title' => 'فاتورة كهرباء',
                'description' => 'فاتورة كهرباء شهر ديسمبر',
                'amount' => 500.00,
                'category' => 'utilities',
                'expense_date' => now(),
                'payment_method' => 'cash',
                'user_id' => $user->id,
                'is_recurring' => true,
                'recurring_frequency' => 'monthly'
            ]);
            $this->line("✅ تم إنشاء المصروف: {$expense->title} (ID: {$expense->id})");

            // 8. اختبار الإعدادات
            $this->info('8️⃣ اختبار نموذج Setting:');
            Setting::set('company_name', 'شركة المالية المتقدمة', 'string', 'company');
            Setting::set('tax_rate', 14, 'float', 'tax');
            $companyName = Setting::get('company_name');
            $taxRate = Setting::get('tax_rate');
            $this->line("✅ تم إنشاء الإعدادات: {$companyName}, معدل الضريبة: {$taxRate}%");

            // 9. اختبار العلاقات
            $this->info('9️⃣ اختبار العلاقات:');
            $categoryProducts = $category->products()->count();
            $customerSales = $customer->sales()->count();
            $userSales = $user->sales()->count();
            $this->line("✅ منتجات التصنيف: {$categoryProducts}");
            $this->line("✅ مبيعات العميل: {$customerSales}");
            $this->line("✅ مبيعات المستخدم: {$userSales}");

            // 10. اختبار الـ Scopes
            $this->info('🔍 اختبار الـ Scopes:');
            $activeProducts = Product::active()->count();
            $todaySales = Sale::today()->count();
            $this->line("✅ المنتجات النشطة: {$activeProducts}");
            $this->line("✅ مبيعات اليوم: {$todaySales}");

            $this->info('🎉 تم اختبار جميع النماذج بنجاح!');
            $this->info('✅ جميع النماذج والعلاقات تعمل بشكل صحيح');

        } catch (\Exception $e) {
            $this->error("❌ خطأ في الاختبار: " . $e->getMessage());
            $this->error("📍 الملف: " . $e->getFile() . " السطر: " . $e->getLine());
        }
    }
}
