@tailwind base;
@tailwind components;
@tailwind utilities;

body {
  background: linear-gradient(135deg, #ecf0f3 0%, #f8fafc 100%);
  font-family: '<PERSON><PERSON><PERSON>', sans-serif;
  direction: rtl;
  font-size: 16px;
  line-height: 1.6;
  min-height: 100vh;
}

/* Enhanced Neumorphism */
.neumorphic {
  border-radius: 20px;
  background: #ecf0f3;
  box-shadow: 12px 12px 24px #d1d9e6,
              -12px -12px 24px #ffffff;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.neumorphic:hover {
  box-shadow: 8px 8px 16px #d1d9e6,
              -8px -8px 16px #ffffff;
  transform: translateY(-2px);
}

.neumorphic-inner {
  border-radius: 15px;
  background: #ecf0f3;
  box-shadow: inset 7px 7px 14px #d1d9e6,
              inset -7px -7px 14px #ffffff;
  transition: all 0.3s ease;
}

.neumorphic-inner:focus {
  box-shadow: inset 5px 5px 10px #d1d9e6,
              inset -5px -5px 10px #ffffff,
              0 0 0 3px rgba(52, 152, 219, 0.1);
  outline: none;
}

/* Button Styles */
.btn-primary {
  background: linear-gradient(135deg, #3498db, #2980b9);
  color: white;
  border: none;
  border-radius: 12px;
  padding: 12px 24px;
  font-weight: 600;
  transition: all 0.3s ease;
  box-shadow: 0 4px 15px rgba(52, 152, 219, 0.3);
}

.btn-primary:hover {
  background: linear-gradient(135deg, #2980b9, #1f5f8b);
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(52, 152, 219, 0.4);
}

.btn-success {
  background: linear-gradient(135deg, #2ecc71, #27ae60);
  color: white;
  border: none;
  border-radius: 12px;
  padding: 12px 24px;
  font-weight: 600;
  transition: all 0.3s ease;
  box-shadow: 0 4px 15px rgba(46, 204, 113, 0.3);
}

.btn-success:hover {
  background: linear-gradient(135deg, #27ae60, #1e8449);
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(46, 204, 113, 0.4);
}

.btn-danger {
  background: linear-gradient(135deg, #e74c3c, #c0392b);
  color: white;
  border: none;
  border-radius: 12px;
  padding: 12px 24px;
  font-weight: 600;
  transition: all 0.3s ease;
  box-shadow: 0 4px 15px rgba(231, 76, 60, 0.3);
}

.btn-danger:hover {
  background: linear-gradient(135deg, #c0392b, #a93226);
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(231, 76, 60, 0.4);
}

/* Utilities */
.text-primary {
  color: #3498db;
}

.text-success {
  color: #2ecc71;
}

.text-danger {
  color: #e74c3c;
}

/* Table Improvements */
.table-modern {
  border-collapse: separate;
  border-spacing: 0;
  border-radius: 15px;
  overflow: hidden;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.table-modern thead th {
  background: linear-gradient(135deg, #f8f9fa, #e9ecef);
  padding: 16px;
  font-weight: 600;
  color: #495057;
  border-bottom: 2px solid #dee2e6;
}

.table-modern tbody tr {
  background: white;
  transition: all 0.3s ease;
}

.table-modern tbody tr:hover {
  background: #f8f9fa;
  transform: scale(1.01);
}

.table-modern tbody td {
  padding: 16px;
  border-bottom: 1px solid #f1f3f4;
}

/* Animation Classes */
.fade-in {
  animation: fadeIn 0.5s ease-in;
}

.slide-up {
  animation: slideUp 0.3s ease-out;
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Loading Spinner */
.spinner {
  border: 3px solid #f3f3f3;
  border-top: 3px solid #3498db;
  border-radius: 50%;
  width: 24px;
  height: 24px;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Notification Styles */
.notification {
  position: fixed;
  top: 20px;
  right: 20px;
  padding: 16px 24px;
  border-radius: 12px;
  color: white;
  font-weight: 600;
  z-index: 1000;
  transform: translateX(400px);
  transition: transform 0.3s ease;
}

.notification.show {
  transform: translateX(0);
}

.notification.success {
  background: linear-gradient(135deg, #2ecc71, #27ae60);
}

.notification.error {
  background: linear-gradient(135deg, #e74c3c, #c0392b);
}

.notification.info {
  background: linear-gradient(135deg, #3498db, #2980b9);
}
