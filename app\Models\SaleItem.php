<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class SaleItem extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'sale_id',
        'product_id',
        'product_name',
        'product_barcode',
        'quantity',
        'unit_price',
        'total_price',
        'discount_amount',
    ];

    /**
     * The attributes that should be cast.
     *
     * @return array<string, string>
     */
    protected function casts(): array
    {
        return [
            'quantity' => 'integer',
            'unit_price' => 'decimal:2',
            'total_price' => 'decimal:2',
            'discount_amount' => 'decimal:2',
        ];
    }

    /**
     * Get the sale that owns the sale item.
     */
    public function sale()
    {
        return $this->belongsTo(Sale::class);
    }

    /**
     * Get the product that owns the sale item.
     */
    public function product()
    {
        return $this->belongsTo(Product::class);
    }

    /**
     * Get the profit for this item.
     */
    public function getProfitAttribute()
    {
        if ($this->product && $this->product->cost_price) {
            return ($this->unit_price - $this->product->cost_price) * $this->quantity;
        }
        return 0;
    }

    /**
     * Get the profit margin for this item.
     */
    public function getProfitMarginAttribute()
    {
        if ($this->product && $this->product->cost_price > 0) {
            return (($this->unit_price - $this->product->cost_price) / $this->product->cost_price) * 100;
        }
        return 0;
    }

    /**
     * Get the final price after discount.
     */
    public function getFinalPriceAttribute()
    {
        return $this->total_price - $this->discount_amount;
    }

    /**
     * Boot the model.
     */
    protected static function boot()
    {
        parent::boot();

        static::creating(function ($saleItem) {
            // Store product details at the time of sale
            if ($saleItem->product) {
                $saleItem->product_name = $saleItem->product->name;
                $saleItem->product_barcode = $saleItem->product->barcode;
            }
            
            // Calculate total price if not set
            if (!$saleItem->total_price) {
                $saleItem->total_price = $saleItem->quantity * $saleItem->unit_price;
            }
        });

        static::updating(function ($saleItem) {
            // Recalculate total price when quantity or unit price changes
            $saleItem->total_price = $saleItem->quantity * $saleItem->unit_price;
        });
    }
}
