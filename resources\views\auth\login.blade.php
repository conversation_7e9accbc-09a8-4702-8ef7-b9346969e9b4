@extends('layouts.app')

@section('content')
<div class="flex items-center justify-center min-h-screen">
    <div class="neumorphic p-8 rounded-xl w-full max-w-md">
        <h2 class="text-2xl font-bold text-center mb-6">تسجيل الدخول</h2>
        <form x-data="{ password: '' }" @submit.prevent="login(password)">
            <div class="mb-4">
                <label for="password" class="block text-gray-700 mb-2">كلمة المرور</label>
                <input type="password" id="password" x-model="password" class="neumorphic-inner w-full p-3 rounded-lg" required>
            </div>
            <button type="submit" class="bg-primary text-white font-bold py-3 px-6 rounded-xl w-full">دخول</button>
        </form>
    </div>
</div>

<script>
    function login() {
        const password = this.password;
        
        // Get stored password or set initial password
        let storedPassword = localStorage.getItem('system_password');
        if (!storedPassword) {
            // First time use, set the password
            localStorage.setItem('system_password', password);
            window.location.href = '/dashboard';
        } else if (storedPassword === password) {
            window.location.href = '/dashboard';
        } else {
            alert('كلمة المرور غير صحيحة');
        }
    }
</script>
@endsection