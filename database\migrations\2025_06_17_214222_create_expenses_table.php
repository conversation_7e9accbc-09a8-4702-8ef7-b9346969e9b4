<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('expenses', function (Blueprint $table) {
            $table->id();
            $table->string('title');
            $table->text('description')->nullable();
            $table->decimal('amount', 10, 2);
            $table->enum('category', ['rent', 'utilities', 'supplies', 'marketing', 'maintenance', 'transportation', 'insurance', 'taxes', 'salaries', 'other'])->default('other');
            $table->timestamp('expense_date');
            $table->enum('payment_method', ['cash', 'card', 'transfer', 'check', 'other'])->default('cash');
            $table->string('receipt_number')->nullable();
            $table->foreignId('user_id')->constrained()->onDelete('cascade');
            $table->boolean('is_recurring')->default(false);
            $table->enum('recurring_frequency', ['daily', 'weekly', 'monthly', 'quarterly', 'yearly'])->nullable();
            $table->timestamp('next_due_date')->nullable();
            $table->timestamps();
            $table->softDeletes();

            // Indexes
            $table->index('title');
            $table->index('category');
            $table->index('expense_date');
            $table->index('user_id');
            $table->index('is_recurring');
            $table->index('next_due_date');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('expenses');
    }
};
