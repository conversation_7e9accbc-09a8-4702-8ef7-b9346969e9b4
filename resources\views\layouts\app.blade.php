<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="X-UA-Compatible" content="ie=edge">
    <title>نظام المالية - POS والمحاسبة</title>
    <meta name="csrf-token" content="{{ csrf_token() }}">

    <!-- Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Tajawal:wght@300;400;500;700&display=swap" rel="stylesheet">

    <!-- Styles -->
    @vite(['resources/css/app.css'])
</head>
<body class="bg-gradient-to-br from-gray-50 to-gray-100 min-h-screen">
    <div id="app" x-data="appData()" class="min-h-screen flex flex-col">
        <!-- Notification Container -->
        <div id="notification-container" class="fixed top-4 right-4 z-50"></div>

        <!-- Header -->
        <header class="neumorphic mx-4 mt-4 rounded-2xl">
            <div class="container mx-auto flex flex-col lg:flex-row justify-between items-center gap-4 p-6">
                <div class="flex items-center gap-4">
                    <div class="w-12 h-12 bg-gradient-to-br from-blue-500 to-blue-600 rounded-xl flex items-center justify-center">
                        <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"></path>
                        </svg>
                    </div>
                    <h1 class="text-2xl lg:text-3xl font-bold text-primary">نظام المالية</h1>
                </div>

                <nav class="w-full lg:w-auto">
                    <ul class="flex flex-wrap justify-center lg:justify-end gap-2">
                        <li>
                            <a href="/dashboard"
                               class="nav-link {{ request()->is('dashboard') ? 'active' : '' }}"
                               data-page="dashboard">
                                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2H5a2 2 0 00-2-2z"></path>
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 5a2 2 0 012-2h4a2 2 0 012 2v6H8V5z"></path>
                                </svg>
                                الرئيسية
                            </a>
                        </li>
                        <li>
                            <a href="/pos"
                               class="nav-link {{ request()->is('pos') ? 'active' : '' }}"
                               data-page="pos">
                                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 9V7a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2m2 4h10a2 2 0 002-2v-6a2 2 0 00-2-2H9a2 2 0 00-2 2v6a2 2 0 002 2zm7-5a2 2 0 11-4 0 2 2 0 014 0z"></path>
                                </svg>
                                نقطة البيع
                            </a>
                        </li>
                        <li>
                            <a href="/products"
                               class="nav-link {{ request()->is('products') ? 'active' : '' }}"
                               data-page="products">
                                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4"></path>
                                </svg>
                                المنتجات
                            </a>
                        </li>
                        <li>
                            <a href="/customers"
                               class="nav-link {{ request()->is('customers') ? 'active' : '' }}"
                               data-page="customers">
                                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z"></path>
                                </svg>
                                العملاء
                            </a>
                        </li>
                        <li>
                            <button @click="logout"
                                    class="nav-link text-red-600 hover:text-red-700 hover:bg-red-50">
                                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1"></path>
                                </svg>
                                تسجيل الخروج
                            </button>
                        </li>
                    </ul>
                </nav>
            </div>
        </header>

        <!-- Main Content -->
        <main class="flex-grow py-6 px-4">
            <div class="container mx-auto fade-in">
                @yield('content')
            </div>
        </main>

        <!-- Footer -->
        <footer class="neumorphic mx-4 mb-4 rounded-2xl">
            <div class="container mx-auto text-center text-gray-600 p-4">
                <div class="flex flex-col md:flex-row justify-between items-center gap-4">
                    <p>&copy; {{ date('Y') }} نظام المالية - POS والمحاسبة</p>
                    <p class="text-sm">تم التطوير بواسطة فريق التطوير المحترف</p>
                </div>
            </div>
        </footer>
    </div>

    <!-- Scripts -->
    @vite(['resources/js/app.js'])

    <script>
        function appData() {
            return {
                init() {
                    // Initialize app
                    this.setActiveNavLink();
                },

                setActiveNavLink() {
                    const currentPath = window.location.pathname;
                    document.querySelectorAll('.nav-link').forEach(link => {
                        link.classList.remove('active');
                        if (link.getAttribute('href') === currentPath) {
                            link.classList.add('active');
                        }
                    });
                },

                logout() {
                    if (confirm('هل أنت متأكد من تسجيل الخروج؟')) {
                        localStorage.removeItem('system_password');
                        window.location.href = '/';
                    }
                },

                showNotification(message, type = 'success') {
                    const container = document.getElementById('notification-container');
                    const notification = document.createElement('div');
                    notification.className = `notification ${type}`;
                    notification.textContent = message;

                    container.appendChild(notification);

                    setTimeout(() => {
                        notification.classList.add('show');
                    }, 100);

                    setTimeout(() => {
                        notification.classList.remove('show');
                        setTimeout(() => {
                            container.removeChild(notification);
                        }, 300);
                    }, 3000);
                }
            }
        }
    </script>

    <style>
        .nav-link {
            @apply flex items-center gap-2 text-gray-700 hover:text-primary font-medium py-3 px-4 rounded-xl transition-all duration-300 hover:bg-white hover:shadow-md;
        }

        .nav-link.active {
            @apply text-primary bg-white shadow-md;
        }

        .nav-link:hover {
            transform: translateY(-1px);
        }
    </style>
</body>
</html>