# ملخص النماذج (Models) المُنشأة

تم إنشاء جميع النماذج المطلوبة بنجاح مع العلاقات والخصائص المناسبة:

## 📋 النماذج المُنشأة:

### 1. **User** (المستخدمين) - `app/Models/User.php`
- **الخصائص**: name, email, password, role, phone, is_active
- **العلاقات**: 
  - hasMany(Sale::class) - المبيعات
  - hasMany(Expense::class) - المصروفات
- **الوظائف**: isAdmin(), isCashier()

### 2. **Category** (التصنيفات) - `app/Models/Category.php`
- **الخصائص**: name, description, is_active
- **العلاقات**: 
  - hasMany(Product::class) - المنتجات
- **الوظائف**: scopeActive(), getProductsCountAttribute()
- **المميزات**: SoftDeletes

### 3. **Product** (المنتجات) - `app/Models/Product.php`
- **الخصائص**: name, description, barcode, price, cost_price, quantity, min_quantity, category_id, image, is_active
- **العلاقات**: 
  - belongsTo(Category::class) - التصنيف
  - hasMany(SaleItem::class) - عناصر المبيعات
- **الوظائف**: 
  - scopeActive(), scopeLowStock(), scopeInStock()
  - isInStock(), hasLowStock()
  - getProfitMarginAttribute(), getTotalValueAttribute()
  - reduceQuantity(), increaseQuantity()
- **المميزات**: SoftDeletes

### 4. **Customer** (العملاء) - `app/Models/Customer.php`
- **الخصائص**: name, email, phone, address, city, postal_code, tax_number, credit_limit, is_active
- **العلاقات**: 
  - hasMany(Sale::class) - المبيعات
- **الوظائف**: 
  - scopeActive()
  - getTotalPurchasesAttribute(), getOutstandingBalanceAttribute()
  - getOrdersCountAttribute(), getLastPurchaseDateAttribute()
  - hasOutstandingBalance(), isWithinCreditLimit()
  - getFullAddressAttribute()
- **المميزات**: SoftDeletes

### 5. **Sale** (المبيعات) - `app/Models/Sale.php`
- **الخصائص**: invoice_number, customer_id, user_id, sale_date, subtotal, tax_amount, discount_amount, total_amount, paid_amount, payment_method, payment_status, notes, status
- **العلاقات**: 
  - belongsTo(Customer::class) - العميل
  - belongsTo(User::class) - المستخدم
  - hasMany(SaleItem::class) - عناصر المبيعات
- **الوظائف**: 
  - scopeCompleted(), scopePending(), scopePaid(), scopeUnpaid()
  - scopeToday(), scopeThisMonth()
  - getRemainingBalanceAttribute(), getTotalProfitAttribute()
  - isFullyPaid(), isPartiallyPaid()
  - generateInvoiceNumber()
- **المميزات**: SoftDeletes, Auto-generate invoice number

### 6. **SaleItem** (عناصر المبيعات) - `app/Models/SaleItem.php`
- **الخصائص**: sale_id, product_id, product_name, product_barcode, quantity, unit_price, total_price, discount_amount
- **العلاقات**: 
  - belongsTo(Sale::class) - المبيعة
  - belongsTo(Product::class) - المنتج
- **الوظائف**: 
  - getProfitAttribute(), getProfitMarginAttribute()
  - getFinalPriceAttribute()
- **المميزات**: Auto-calculate total price, Store product details at sale time

### 7. **Expense** (المصروفات) - `app/Models/Expense.php`
- **الخصائص**: title, description, amount, category, expense_date, payment_method, receipt_number, user_id, is_recurring, recurring_frequency, next_due_date
- **العلاقات**: 
  - belongsTo(User::class) - المستخدم
- **الوظائف**: 
  - scopeToday(), scopeThisMonth(), scopeThisYear()
  - scopeRecurring(), scopeByCategory()
  - getCategories(), getCategoryNameAttribute()
  - getRecurringFrequencies(), getRecurringFrequencyNameAttribute()
  - calculateNextDueDate(), isDue()
- **المميزات**: SoftDeletes, Recurring expenses support

### 8. **Setting** (الإعدادات) - `app/Models/Setting.php`
- **الخصائص**: key, value, type, group, description
- **الوظائف**: 
  - get(), set(), getAll(), getByGroup()
  - castValue(), getDefaults(), initializeDefaults()
  - getGroups()
- **المميزات**: 
  - إعدادات الشركة، العملة، الضريبة، الفواتير، المخزون، النظام
  - تحويل القيم تلقائياً حسب النوع
  - إعدادات افتراضية شاملة

## 🔗 العلاقات بين النماذج:

```
User (1) -----> (N) Sale
User (1) -----> (N) Expense

Category (1) -----> (N) Product

Customer (1) -----> (N) Sale

Product (1) -----> (N) SaleItem
Category (1) -----> (N) Product

Sale (1) -----> (N) SaleItem
Sale (N) -----> (1) Customer
Sale (N) -----> (1) User

SaleItem (N) -----> (1) Sale
SaleItem (N) -----> (1) Product

Expense (N) -----> (1) User
```

## 📊 المميزات المُضافة:

### ✅ **Soft Deletes**
- Category, Product, Customer, Sale, Expense

### ✅ **Scopes مفيدة**
- Active records
- Date-based queries (Today, This Month, This Year)
- Status-based queries (Paid, Unpaid, Completed, etc.)
- Stock-based queries (Low Stock, In Stock)

### ✅ **Attributes محسوبة**
- Profit margins
- Total values
- Outstanding balances
- Stock status

### ✅ **Auto-calculations**
- Invoice numbers
- Total prices
- Next due dates for recurring expenses

### ✅ **Type Casting**
- Decimal values for money
- Boolean values
- Date/DateTime values
- Integer values

## 🚀 الخطوات التالية:

1. **إنشاء Migrations** لقاعدة البيانات
2. **إنشاء Controllers** للتحكم في البيانات
3. **إنشاء Seeders** للبيانات التجريبية
4. **تحديث Routes** للربط مع Controllers
5. **تحديث Views** للعمل مع قاعدة البيانات

جميع النماذج جاهزة للاستخدام ومُحسّنة للأداء والوظائف المطلوبة! 🎉
