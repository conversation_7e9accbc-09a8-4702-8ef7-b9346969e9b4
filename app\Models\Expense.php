<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class Expense extends Model
{
    use HasFactory, SoftDeletes;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'title',
        'description',
        'amount',
        'category',
        'expense_date',
        'payment_method',
        'receipt_number',
        'user_id',
        'is_recurring',
        'recurring_frequency',
        'next_due_date',
    ];

    /**
     * The attributes that should be cast.
     *
     * @return array<string, string>
     */
    protected function casts(): array
    {
        return [
            'amount' => 'decimal:2',
            'expense_date' => 'datetime',
            'next_due_date' => 'datetime',
            'is_recurring' => 'boolean',
        ];
    }

    /**
     * Get the user that created the expense.
     */
    public function user()
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Scope a query for today's expenses.
     */
    public function scopeToday($query)
    {
        return $query->whereDate('expense_date', today());
    }

    /**
     * Scope a query for this month's expenses.
     */
    public function scopeThisMonth($query)
    {
        return $query->whereMonth('expense_date', now()->month)
                    ->whereYear('expense_date', now()->year);
    }

    /**
     * Scope a query for this year's expenses.
     */
    public function scopeThisYear($query)
    {
        return $query->whereYear('expense_date', now()->year);
    }

    /**
     * Scope a query for recurring expenses.
     */
    public function scopeRecurring($query)
    {
        return $query->where('is_recurring', true);
    }

    /**
     * Scope a query for expenses by category.
     */
    public function scopeByCategory($query, $category)
    {
        return $query->where('category', $category);
    }

    /**
     * Get all expense categories.
     */
    public static function getCategories()
    {
        return [
            'rent' => 'إيجار',
            'utilities' => 'مرافق',
            'supplies' => 'مستلزمات',
            'marketing' => 'تسويق',
            'maintenance' => 'صيانة',
            'transportation' => 'مواصلات',
            'insurance' => 'تأمين',
            'taxes' => 'ضرائب',
            'salaries' => 'رواتب',
            'other' => 'أخرى',
        ];
    }

    /**
     * Get the category name in Arabic.
     */
    public function getCategoryNameAttribute()
    {
        $categories = static::getCategories();
        return $categories[$this->category] ?? $this->category;
    }

    /**
     * Get recurring frequency options.
     */
    public static function getRecurringFrequencies()
    {
        return [
            'daily' => 'يومي',
            'weekly' => 'أسبوعي',
            'monthly' => 'شهري',
            'quarterly' => 'ربع سنوي',
            'yearly' => 'سنوي',
        ];
    }

    /**
     * Get the recurring frequency name in Arabic.
     */
    public function getRecurringFrequencyNameAttribute()
    {
        $frequencies = static::getRecurringFrequencies();
        return $frequencies[$this->recurring_frequency] ?? $this->recurring_frequency;
    }

    /**
     * Calculate next due date for recurring expenses.
     */
    public function calculateNextDueDate()
    {
        if (!$this->is_recurring || !$this->recurring_frequency) {
            return null;
        }

        $baseDate = $this->next_due_date ?: $this->expense_date;

        switch ($this->recurring_frequency) {
            case 'daily':
                return $baseDate->addDay();
            case 'weekly':
                return $baseDate->addWeek();
            case 'monthly':
                return $baseDate->addMonth();
            case 'quarterly':
                return $baseDate->addMonths(3);
            case 'yearly':
                return $baseDate->addYear();
            default:
                return null;
        }
    }

    /**
     * Check if expense is due for recurring.
     */
    public function isDue()
    {
        if (!$this->is_recurring || !$this->next_due_date) {
            return false;
        }

        return $this->next_due_date->isPast();
    }
}
