@extends('layouts.app')

@section('content')
<div class="container mx-auto py-6">
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <!-- Sales Card -->
        <div class="neumorphic p-6 rounded-xl">
            <div class="flex justify-between items-center">
                <div>
                    <p class="text-gray-600">المبيعات اليوم</p>
                    <p class="text-2xl font-bold mt-2">0.00 ج.م</p>
                </div>
                <div class="bg-primary p-3 rounded-full">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                </div>
            </div>
        </div>

        <!-- Products Card -->
        <div class="neumorphic p-6 rounded-xl">
            <div class="flex justify-between items-center">
                <div>
                    <p class="text-gray-600">المنتجات</p>
                    <p class="text-2xl font-bold mt-2" x-text="productCount">0</p>
                </div>
                <div class="bg-success p-3 rounded-full">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 3h2l.4 2M7 13h10l4-8H5.4M7 13L5.4 5M7 13l-2.293 2.293c-.63.63-.184 1.707.707 1.707H17m0 0a2 2 0 100 4 2 2 0 000-4zm-8 2a2 2 0 11-4 0 2 2 0 014 0z" />
                    </svg>
                </div>
            </div>
        </div>

        <!-- Customers Card -->
        <div class="neumorphic p-6 rounded-xl">
            <div class="flex justify-between items-center">
                <div>
                    <p class="text-gray-600">العملاء</p>
                    <p class="text-2xl font-bold mt-2" x-text="customerCount">0</p>
                </div>
                <div class="bg-primary p-3 rounded-full">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
                    </svg>
                </div>
            </div>
        </div>

        <!-- Expenses Card -->
        <div class="neumorphic p-6 rounded-xl">
            <div class="flex justify-between items-center">
                <div>
                    <p class="text-gray-600">المصروفات</p>
                    <p class="text-2xl font-bold mt-2">0.00 ج.م</p>
                </div>
                <div class="bg-danger p-3 rounded-full">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                </div>
            </div>
        </div>
    </div>

    <div class="mt-8 grid grid-cols-1 lg:grid-cols-3 gap-6">
        <!-- Quick Actions -->
        <div class="neumorphic p-6 rounded-xl lg:col-span-1">
            <h2 class="text-xl font-bold mb-4">إجراءات سريعة</h2>
            <div class="space-y-4">
                <button class="bg-primary text-white font-bold py-3 px-6 rounded-xl w-full">بيع جديد</button>
                <button class="btn-success w-full py-3 rounded-lg text-white font-bold">إضافة منتج</button>
                <button class="bg-gray-600 text-white font-bold py-3 px-6 rounded-xl w-full">إضافة عميل</button>
                <button class="bg-purple-600 w-full py-3 rounded-lg text-white font-bold">تسجيل مصروف</button>
            </div>
        </div>

        <!-- Recent Sales -->
        <div class="neumorphic p-6 rounded-xl lg:col-span-2">
            <h2 class="text-xl font-bold mb-4">آخر المبيعات</h2>
            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200">
                    <thead class="bg-gray-50">
                        <tr>
                            <th class="px-4 py-2 text-right">#</th>
                            <th class="px-4 py-2 text-right">العميل</th>
                            <th class="px-4 py-2 text-right">المبلغ</th>
                            <th class="px-4 py-2 text-right">التاريخ</th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        <!-- Will be populated by JavaScript -->
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>

<script>
    document.addEventListener('alpine:init', () => {
        Alpine.data('dashboard', () => ({
            productCount: 0,
            customerCount: 0,
            
            init() {
                // Load counts from localStorage
                const products = JSON.parse(localStorage.getItem('products') || '[]');
                const customers = JSON.parse(localStorage.getItem('customers') || '[]');
                
                this.productCount = products.length;
                this.customerCount = customers.length;
            }
        }));
    });
</script>
@endsection